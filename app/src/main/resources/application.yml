spring:
  profiles:
    active: local
  application:
    name: field-mapping-service

  main:
    banner-mode: "off"

  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      auto-commit: false
      max-lifetime: 590000
      minimum-idle: 2
      maximum-pool-size: 5
      idle-timeout: 300000

  jpa:
    open_in_view: false
    properties:
      hibernate:
        physical_naming_strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
        connection:
          provider_disables_autocommit: true
        jdbc:
          time_zone: UTC
          batch_size: 25
        batch_versioned_data: true
        order_inserts: true
        order_updates: true
        cache:
          use_second_level_cache: false
          use_query_cache: false
        query:
          fail_on_pagination_over_collection_fetch: false
          in_clause_parameter_padding: true
        hbm2ddl:
          auto: none
        show_sql: false
        format_sql: false
        generate_statistics: false

  liquibase:
    enabled: true
    changeLog: classpath:com/multiplier/fieldmapping/liquibase/master.xml
    liquibase-schema: public
    default-schema: field_mapping

grpc:
  client:
#    authority-service:
#      address: ${GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS}
#      enableKeepAlive: true
#      keepAliveWithoutCalls: true
#      negotiationType: TLS
  server:
    port: 9090
    max-inbound-metadata-size: 2MB
    security:
      enabled: true
      certificate-chain: classpath:certificates/server.crt
      private-key: classpath:certificates/server.key

growthbook:
  base-url: ${GROWTHBOOK_BASEURL}
  env-key: ${GROWTHBOOK_ENVKEY}

kafka:
  groupId: field-mapping-service
  bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}

mpl:
  graphql:
    scalar:
      enabled: true
    error-resolver:
      enabled: true
    instrumentation:
      enabled: true
  grpc:
    coroutine:
      enabled: true
    instrumentation:
      enabled: true
    error-mapping:
      enabled: true
  config:
    aws-parameterstore:
      env: stg
      profiles: growthbook, grpc, kafka

multiplier:
#  ai-service:
#    url: ${AI_SERVICE_URL:http://localhost:8080}
#    api-key: ${AI_SERVICE_API_KEY:}

logging:
  level:
    root: INFO

dgs:
  graphql:
    extensions:
      scalars:
        enabled: false
