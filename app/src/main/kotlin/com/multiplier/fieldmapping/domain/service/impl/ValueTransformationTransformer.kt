package com.multiplier.fieldmapping.domain.service.impl

import com.multiplier.fieldmapping.domain.model.FieldMappingRuleTransformationConfig
import com.multiplier.fieldmapping.domain.service.FieldTransformer

// Value transformation transformer
class ValueTransformationTransformer : FieldTransformer {
    override fun transform(sourceValue: Any?, config: FieldMappingRuleTransformationConfig?): Any? {
        var result = sourceValue

        if (sourceValue != null && config?.mappings != null) {
            if (config.mappings.isNotEmpty()) {
                val mappedValue = config.mappings[sourceValue.toString()]
                if (mappedValue != null) {
                    result = mappedValue
                }
            }
        }

        return result
    }
}
