package com.multiplier.fieldmapping.domain.service.impl

import com.multiplier.common.HasLogger
import com.multiplier.fieldmapping.domain.model.FieldMappingProfile
import com.multiplier.fieldmapping.domain.model.FieldMappingRule
import com.multiplier.fieldmapping.domain.model.TransformationType
import com.multiplier.fieldmapping.domain.service.FieldMappingDomainService
import com.multiplier.fieldmapping.domain.service.TransformationRegistry
import org.springframework.stereotype.Service

@Service
class FieldMappingDomainServiceImpl(
    private val transformationRegistry: TransformationRegistry
) : FieldMappingDomainService, HasLogger {

    override fun transformData(sourceData: Map<String, Any>, profile: FieldMappingProfile): Map<String, Any> {
        log.info { "Transforming data using profile: ${profile.id}" }

        val result = mutableMapOf<String, Any>()

        // Sort rules by order to ensure consistent application
        val sortedRules = profile.rules.sortedBy { it.order }

        sortedRules.forEach { rule ->
            try {
                val transformedValue = applyRule(sourceData, rule)

                // Only add non-null values to the result
                if (transformedValue != null) {
                    result[rule.targetField] = transformedValue
                } else if (rule.defaultValue != null) {
                    result[rule.targetField] = rule.defaultValue?.toString() ?: ""
                } else if (rule.isRequired) {
                    error("Required field ${rule.targetField} has null value and no default")
                }
            } catch (e: IllegalArgumentException) {
                log.error(e) { "Error applying rule: ${rule.id} for field: ${rule.sourceField} -> ${rule.targetField}" }

                if (rule.isRequired) {
                    throw IllegalStateException("Failed to transform required field ${rule.targetField}", e)
                }
            } catch (e: IllegalStateException) {
                log.error(e) { "Error applying rule: ${rule.id} for field: ${rule.sourceField} -> ${rule.targetField}" }

                if (rule.isRequired) {
                    throw IllegalStateException("Failed to transform required field ${rule.targetField}", e)
                }
            }
        }

        return result
    }

    override fun applyRule(sourceData: Map<String, Any>, rule: FieldMappingRule): Any? {
        val transformer = transformationRegistry.getTransformer(rule.transformationType)

        // For direct mapping, get the source field value
        val sourceValue = if (rule.sourceField.contains(".")) {
            // Handle nested fields with dot notation
            getNestedValue(sourceData, rule.sourceField)
        } else {
            sourceData[rule.sourceField]
        }

        // Get the transformation config based on the rule type
        val config = when (rule.transformationType) {
            TransformationType.VALUE_TRANSFORMATION -> rule.transformationConfig
            else -> rule.transformationConfig
        }

        return transformer.transform(sourceValue, config)
    }

    override fun validateProfile(profile: FieldMappingProfile): List<String> {
        val errors = mutableListOf<String>()

        if (profile.name.isBlank()) {
            errors.add("Profile name cannot be blank")
        }

        // Schema name validations removed as schemas are no longer persisted

        // Check for duplicate target fields
        val targetFields = profile.rules.map { it.targetField }
        val duplicateTargetFields = targetFields.groupBy { it }
            .filter { it.value.size > 1 }
            .keys

        if (duplicateTargetFields.isNotEmpty()) {
            errors.add("Duplicate target fields: ${duplicateTargetFields.joinToString(", ")}")
        }

        return errors
    }

    // Made public for testing
    fun getNestedValue(data: Map<String, Any>?, path: String): Any? {
        var result: Any? = null

        if (data != null && path.isNotEmpty()) {
            val parts = path.split("[", "]", ".").filter { it.isNotBlank() }
            var current: Any? = data
            var validPath = true

            for (part in parts) {
                current = when (current) {
                    is Map<*, *> -> (current as Map<String, Any>)[part]
                    is List<*> -> extractFromList(current, part)
                    else -> null
                }

                if (current == null) {
                    validPath = false
                    break
                }
            }

            if (validPath) {
                result = current
            }
        }

        return result
    }
    private fun extractFromList(list: List<*>, key: String): Any? =
        if (key.contains("=")) extractFromListWithConditions(list, key) else extractFromListWithIndex(list, key)

    fun extractFromListWithConditions(list: List<*>, key: String): Any? {
        val filterConditions = key.split(",").map { it.trim() }
        val filteredItems = list.filter {
            val element = it as Map<*, *>?
            element != null && filterConditions.all { condition ->
                val (filterKey, filterValue) = condition.split("=")
                element[filterKey]?.toString() == filterValue
            }
        }
        return filteredItems.firstOrNull()
    }

    fun extractFromListWithIndex(list: List<*>, key: String): Any? =
        key.toIntOrNull()?.let { if (it in list.indices) list[it] else null }
}
