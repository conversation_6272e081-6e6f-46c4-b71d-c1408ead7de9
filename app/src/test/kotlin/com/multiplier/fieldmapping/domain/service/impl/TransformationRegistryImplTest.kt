package com.multiplier.fieldmapping.domain.service.impl

import com.multiplier.fieldmapping.domain.model.Condition
import com.multiplier.fieldmapping.domain.model.FieldMappingRuleTransformationConfig
import com.multiplier.fieldmapping.domain.model.TransformationType
import com.multiplier.fieldmapping.domain.service.FieldTransformer
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@Suppress("TooManyFunctions")
class TransformationRegistryImplTest {

    private lateinit var registry: TransformationRegistryImpl

    @BeforeEach
    fun setUp() {
        registry = TransformationRegistryImpl()
        registry.init()
    }

    @Test
    fun `getTransformer should return registered transformer`() {
        // When
        val transformer = registry.getTransformer(TransformationType.DIRECT_MAPPING)

        // Then
        assertNotNull(transformer)
        assertTrue(transformer is DirectMappingTransformer)
    }

    @Test
    fun `getTransformer should throw exception for unregistered transformer`() {
        // Given
        val customRegistry = TransformationRegistryImpl()
        // Note: not calling init() to have an empty registry

        // When/Then
        val exception = assertThrows<IllegalArgumentException> {
            customRegistry.getTransformer(TransformationType.DIRECT_MAPPING)
        }
        assertTrue(exception.message?.contains("No transformer registered") == true)
    }

    @Test
    fun `registerTransformer should add new transformer`() {
        // Given
        val customRegistry = TransformationRegistryImpl()
        val mockTransformer = object : FieldTransformer {
            override fun transform(sourceValue: Any?, config: FieldMappingRuleTransformationConfig?): Any? {
                return "transformed"
            }
        }

        // When
        customRegistry.registerTransformer(TransformationType.DIRECT_MAPPING, mockTransformer)
        val retrievedTransformer = customRegistry.getTransformer(TransformationType.DIRECT_MAPPING)

        // Then
        assertEquals(mockTransformer, retrievedTransformer)
        assertEquals("transformed", retrievedTransformer.transform("test", null))
    }

    @Test
    fun `DirectMappingTransformer should return source value unchanged`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.DIRECT_MAPPING)
        val sourceValue = "test value"

        // When
        val result = transformer.transform(sourceValue, null)

        // Then
        assertEquals(sourceValue, result)
    }

    @Test
    fun `ValueTransformationTransformer should map values according to config`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.VALUE_TRANSFORMATION)
        val sourceValue = "A"
        val config = FieldMappingRuleTransformationConfig(
            mappings = mapOf(
                "A" to "Alpha",
                "B" to "Bravo"
            )
        )

        // When
        val result = transformer.transform(sourceValue, config)

        // Then
        assertEquals("Alpha", result)
    }

    @Test
    fun `ValueTransformationTransformer should return original value if no mapping found`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.VALUE_TRANSFORMATION)
        val sourceValue = "C"
        val config = FieldMappingRuleTransformationConfig(
            mappings = mapOf(
                "A" to "Alpha",
                "B" to "Bravo"
            )
        )

        // When
        val result = transformer.transform(sourceValue, config)

        // Then
        assertEquals(sourceValue, result)
    }

    @Test
    fun `BooleanConversionTransformer should convert various formats to boolean`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.BOOLEAN_CONVERSION)

        // When/Then
        assertEquals(true, transformer.transform("true", null))
        assertEquals(true, transformer.transform("yes", null))
        assertEquals(true, transformer.transform("Y", null))
        assertEquals(true, transformer.transform("1", null))
        assertEquals(true, transformer.transform("ON", null))

        assertEquals(false, transformer.transform("false", null))
        assertEquals(false, transformer.transform("no", null))
        assertEquals(false, transformer.transform("N", null))
        assertEquals(false, transformer.transform("0", null))
        assertEquals(false, transformer.transform("OFF", null))

        assertNull(transformer.transform("invalid", null))
    }

    @Test
    fun `ConditionalMappingTransformer should apply conditions correctly`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.CONDITIONAL_MAPPING)
        val sourceValue = 25
        val config = FieldMappingRuleTransformationConfig(
            conditions = listOf(
                Condition(
                    operator = "gt",
                    value = 30,
                    result = "High"
                ),
                Condition(
                    operator = "gt",
                    value = 20,
                    result = "Medium"
                ),
                Condition(
                    operator = "gt",
                    value = 10,
                    result = "Low"
                )
            ),
            default = "Very Low"
        )

        // When
        val result = transformer.transform(sourceValue, config)

        // Then
        assertEquals("Medium", result)
    }

    @Test
    fun `ConditionalMappingTransformer should use default when no conditions match`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.CONDITIONAL_MAPPING)
        val sourceValue = 5
        val config = FieldMappingRuleTransformationConfig(
            conditions = listOf(
                Condition(
                    operator = "gt",
                    value = 10,
                    result = "Low"
                )
            ),
            default = "Very Low"
        )
        mapOf(
            "conditions" to listOf(
                mapOf(
                    "operator" to "gt",
                    "value" to 10,
                    "result" to "Low"
                )
            ),
            "default" to "Very Low"
        )

        // When
        val result = transformer.transform(sourceValue, config)

        // Then
        assertEquals("Very Low", result)
    }

    @Test
    fun `ConditionalMappingTransformer should handle string comparisons`() {
        // Given
        val transformer = registry.getTransformer(TransformationType.CONDITIONAL_MAPPING)
        val sourceValue = "Hello World"
        val config = FieldMappingRuleTransformationConfig(
            conditions = listOf(
                Condition(
                    operator = "contains",
                    value = "World",
                    result = "Contains World"
                ),
                Condition(
                    operator = "startsWith",
                    value = "Goodbye",
                    result = "Starts with Goodbye"
                )
            ),
        )

        // When
        val result = transformer.transform(sourceValue, config)

        // Then
        assertEquals("Contains World", result)
    }
}
