package com.multiplier.fieldmapping.domain.service

import com.multiplier.fieldmapping.domain.model.FieldMappingRuleTransformationConfig
import com.multiplier.fieldmapping.domain.model.TransformationType
import com.multiplier.fieldmapping.domain.service.impl.TransformationRegistryImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.stream.Stream

class TransformationRegistryImplTest {

    private lateinit var transformationRegistry: TransformationRegistryImpl

    @BeforeEach
    fun setUp() {
        transformationRegistry = TransformationRegistryImpl().apply { init() }
    }

    @Test
    fun `should register all default transformers during initialization`() {
        // Test that all transformation types have registered transformers
        val expectedTypes = listOf(
            TransformationType.DIRECT_MAPPING,
            TransformationType.VALUE_TRANSFORMATION,
            TransformationType.CONCATENATION,
            TransformationType.SPLIT,
            TransformationType.DATE_FORMAT_CONVERSION,
            TransformationType.REGEX_EXTRACTION,
            TransformationType.BOOLEAN_CONVERSION,
            TransformationType.NUMERIC_TRANSFORMATION,
            TransformationType.CONDITIONAL_MAPPING,
            TransformationType.FUNCTIONAL_TRANSFORMATION
        )

        expectedTypes.forEach { type ->
            assertNotNull(transformationRegistry.getTransformer(type), "Transformer not found for type: $type")
        }
    }

    @Test
    fun `should throw exception for unregistered transformer type`() {
        // Create a custom transformation registry without initialization
        val emptyRegistry = TransformationRegistryImpl()

        val exception = assertThrows(IllegalArgumentException::class.java) {
            emptyRegistry.getTransformer(TransformationType.DIRECT_MAPPING)
        }

        assertEquals("No transformer registered for type: DIRECT_MAPPING", exception.message)
    }

    @Test
    fun `DirectMappingTransformer should return source value unchanged`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.DIRECT_MAPPING)

        val testCases = listOf(
            "test string",
            123,
            true,
            null,
            listOf("a", "b", "c"),
            mapOf("key" to "value")
        )

        testCases.forEach { input ->
            val result = transformer.transform(input, null)
            assertEquals(input, result, "Direct mapping failed for input: $input")
        }
    }

    @Test
    fun `ValueTransformationTransformer should map values correctly`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.VALUE_TRANSFORMATION)

        val config = FieldMappingRuleTransformationConfig(
            mappings = mapOf(
                "M" to "Male",
                "F" to "Female",
                "U" to "Unknown"
            )
        )

        assertEquals("Male", transformer.transform("M", config))
        assertEquals("Female", transformer.transform("F", config))
        assertEquals("Unknown", transformer.transform("U", config))
        assertEquals("X", transformer.transform("X", config)) // Unmapped value returns as-is
    }

    @Test
    fun `ValueTransformationTransformer should handle null and missing config`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.VALUE_TRANSFORMATION)

        assertEquals("test", transformer.transform("test", null))
        assertEquals(
            "test", transformer.transform(
                "test", FieldMappingRuleTransformationConfig(
                    mappings = emptyMap()
                )
            )
        )
        assertEquals(
            null,
            transformer.transform(null, FieldMappingRuleTransformationConfig(mappings = mapOf("a" to "b")))
        )
    }

    @Test
    fun `FunctionalTransformationTransformer should apply count function correctly`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.FUNCTIONAL_TRANSFORMATION)

        val config = FieldMappingRuleTransformationConfig(
            function = "COUNT"
        )

        assertEquals("3", transformer.transform(listOf("a", "b", "c"), config))
        assertEquals("2", transformer.transform(arrayOf("x", "y"), config))
        assertEquals("0", transformer.transform("not a collection", config))
    }

    @Test
    fun `FunctionalTransformationTransformer should apply country alpha3 function correctly`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.FUNCTIONAL_TRANSFORMATION)

        val config = FieldMappingRuleTransformationConfig(
            function = "COUNTRY_ALPHA_3_CODE"
        )

        assertEquals("USA", transformer.transform("US", config))
        assertEquals("CAN", transformer.transform("Canada", config))
        assertEquals("INVALID", transformer.transform("INVALID", config)) // Should return original for invalid
    }

    companion object {
        @JvmStatic
        fun dateFormatTestCases(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("2023-12-25", "yyyy-MM-dd", "dd/MM/yyyy", "25/12/2023"),
                Arguments.of("25/12/2023", "dd/MM/yyyy", "yyyy-MM-dd", "2023-12-25"),
                Arguments.of("2023-12-25T10:30:00", "yyyy-MM-dd'T'HH:mm:ss", "dd-MM-yyyy HH:mm", "25-12-2023 10:30")
            )
        }
    }

    @Test
    fun `transform should return null for unknown function`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.FUNCTIONAL_TRANSFORMATION)
        val config = FieldMappingRuleTransformationConfig(function = "UNKNOWN_FUNCTION")
        val result = transformer.transform("input", config)
        assertEquals("input", result)
    }

    @Test
    fun `count should return 0 for unsupported data type`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.FUNCTIONAL_TRANSFORMATION)
        val result = transformer.javaClass.getDeclaredMethod("count", Any::class.java)
            .apply { isAccessible = true }
            .invoke(transformer, 123)
        assertEquals("0", result)
    }

    @Test
    fun `FunctionalTransformationTransformer should map to alpha-2 country code`() {
        val transformer = transformationRegistry.getTransformer(TransformationType.FUNCTIONAL_TRANSFORMATION)
        val config = FieldMappingRuleTransformationConfig(function = "COUNTRY_ALPHA_2_CODE")
        val result = transformer.transform("United States", config)
        // Adjust the expected value based on your mapToAlpha2CountryCode implementation
        assertEquals("US", result)
    }
}
