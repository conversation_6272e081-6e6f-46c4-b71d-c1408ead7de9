package com.multiplier.fieldmapping.domain.service

import com.multiplier.fieldmapping.domain.model.FieldMappingProfile
import com.multiplier.fieldmapping.domain.model.FieldMappingRule
import com.multiplier.fieldmapping.domain.model.TransformationType
import com.multiplier.fieldmapping.domain.service.impl.FieldMappingDomainServiceImpl
import com.multiplier.fieldmapping.domain.service.impl.TransformationRegistryImpl
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import java.util.stream.Stream

class FieldMappingDomainServiceImplTest {

    private lateinit var domainService: FieldMappingDomainServiceImpl
    private lateinit var transformationRegistry: TransformationRegistry

    @BeforeEach
    fun setUp() {
        transformationRegistry = TransformationRegistryImpl().apply { init() }
        domainService = FieldMappingDomainServiceImpl(transformationRegistry)
    }

    @Test
    fun `transformData should apply rules in correct order`() {
        // Given
        val sourceData = mapOf(
            "firstName" to "John",
            "lastName" to "Doe",
            "age" to 30
        )

        val rules = listOf(
            createRule("rule1", "firstName", "first_name", TransformationType.DIRECT_MAPPING, order = 2),
            createRule("rule2", "lastName", "last_name", TransformationType.DIRECT_MAPPING, order = 1),
            createRule("rule3", "age", "user_age", TransformationType.DIRECT_MAPPING, order = 3)
        )

        val profile = createProfile(rules)

        // When
        val result = domainService.transformData(sourceData, profile)

        // Then
        assertEquals(3, result.size)
        assertEquals("John", result["first_name"])
        assertEquals("Doe", result["last_name"])
        assertEquals(30, result["user_age"])
    }

    @Test
    fun `transformData should handle required fields with null values and default values`() {
        // Given
        val sourceData = mapOf(
            "firstName" to "John"
            // lastName is missing
        )

        val rules = listOf(
            createRule("rule1", "firstName", "first_name", TransformationType.DIRECT_MAPPING, isRequired = false),
            createRule("rule2", "lastName", "last_name", TransformationType.DIRECT_MAPPING, 
                      isRequired = true, defaultValue = "Unknown")
        )

        val profile = createProfile(rules)

        // When
        val result = domainService.transformData(sourceData, profile)

        // Then
        assertEquals(2, result.size)
        assertEquals("John", result["first_name"])
        assertEquals("Unknown", result["last_name"])
    }

    @Test
    fun `transformData should throw exception for required field with null value and no default`() {
        // Given
        val sourceData = mapOf(
            "firstName" to "John"
            // lastName is missing
        )

        val rules = listOf(
            createRule("rule1", "firstName", "first_name", TransformationType.DIRECT_MAPPING),
            createRule("rule2", "lastName", "last_name", TransformationType.DIRECT_MAPPING, isRequired = true)
        )

        val profile = createProfile(rules)

        // When & Then
        val exception = assertThrows(IllegalStateException::class.java) {
            domainService.transformData(sourceData, profile)
        }

        assertTrue(exception.message!!.contains("Failed to transform required field last_name"))
    }

    @Test
    fun `transformData should skip non-required fields with null values`() {
        // Given
        val sourceData = mapOf(
            "firstName" to "John"
            // lastName is missing
        )

        val rules = listOf(
            createRule("rule1", "firstName", "first_name", TransformationType.DIRECT_MAPPING),
            createRule("rule2", "lastName", "last_name", TransformationType.DIRECT_MAPPING, isRequired = false)
        )

        val profile = createProfile(rules)

        // When
        val result = domainService.transformData(sourceData, profile)

        // Then
        assertEquals(1, result.size)
        assertEquals("John", result["first_name"])
        assertTrue(!result.containsKey("last_name"))
    }

    @Test
    fun `transformData should handle nested field access`() {
        // Given
        val sourceData = mapOf(
            "user" to mapOf(
                "profile" to mapOf(
                    "firstName" to "John",
                    "lastName" to "Doe"
                ),
                "contact" to mapOf(
                    "email" to "<EMAIL>"
                )
            )
        )

        val rules = listOf(
            createRule("rule1", "user.profile.firstName", "first_name", TransformationType.DIRECT_MAPPING),
            createRule("rule2", "user.profile.lastName", "last_name", TransformationType.DIRECT_MAPPING),
            createRule("rule3", "user.contact.email", "email", TransformationType.DIRECT_MAPPING)
        )

        val profile = createProfile(rules)

        // When
        val result = domainService.transformData(sourceData, profile)

        // Then
        assertEquals(3, result.size)
        assertEquals("John", result["first_name"])
        assertEquals("Doe", result["last_name"])
        assertEquals("<EMAIL>", result["email"])
    }

    @Test
    fun `getNestedValue should handle simple field access`() {
        // Given
        val data = mapOf(
            "firstName" to "John",
            "age" to 30
        )

        // When & Then
        assertEquals("John", domainService.getNestedValue(data, "firstName"))
        assertEquals(30, domainService.getNestedValue(data, "age"))
        assertNull(domainService.getNestedValue(data, "nonExistent"))
    }

    @Test
    fun `getNestedValue should handle nested field access`() {
        // Given
        val data = mapOf(
            "user" to mapOf(
                "profile" to mapOf(
                    "name" to "John Doe",
                    "age" to 30
                ),
                "settings" to mapOf(
                    "theme" to "dark"
                )
            )
        )

        // When & Then
        assertEquals("John Doe", domainService.getNestedValue(data, "user.profile.name"))
        assertEquals(30, domainService.getNestedValue(data, "user.profile.age"))
        assertEquals("dark", domainService.getNestedValue(data, "user.settings.theme"))
        assertNull(domainService.getNestedValue(data, "user.profile.nonExistent"))
        assertNull(domainService.getNestedValue(data, "user.nonExistent.field"))
    }

    @Test
    fun `getNestedValue should handle deeply nested structures`() {
        // Given
        val data = mapOf(
            "level1" to mapOf(
                "level2" to mapOf(
                    "level3" to mapOf(
                        "level4" to mapOf(
                            "value" to "deep value"
                        )
                    )
                )
            )
        )

        // When & Then
        assertEquals("deep value", domainService.getNestedValue(data, "level1.level2.level3.level4.value"))
        assertNull(domainService.getNestedValue(data, "level1.level2.level3.level4.nonExistent"))
    }

    @Test
    fun `getNestedValue should handle null intermediate values gracefully`() {
        // Given
        val data = mapOf(
            "user" to null,
            "profile" to mapOf(
                "details" to null
            )
        )

        // When & Then
        assertNull(domainService.getNestedValue(data  as Map<String, Any>?, "user.profile.name"))
        assertNull(domainService.getNestedValue(data as Map<String, Any>?, "profile.details.name"))
    }

    @Test
    fun `validateProfile should return no errors for valid profile`() {
        // Given
        val profile = FieldMappingProfile(
            id = "test-id",
            name = "Valid Profile",
            description = "Test profile",
            companyId = 12345L,
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            rules = listOf(
                createRule("rule1", "field1", "target1", TransformationType.DIRECT_MAPPING),
                createRule("rule2", "field2", "target2", TransformationType.DIRECT_MAPPING)
            )
        )

        // When
        val errors = domainService.validateProfile(profile)

        // Then
        assertTrue(errors.isEmpty())
    }

    @Test
    fun `applyRule should handle transformation errors gracefully for non-required fields`() {
        // Given
        val sourceData = mapOf("field1" to "value1")
        
        // Create a rule that will cause a transformation error
        val rule = createRule("rule1", "field1", "target1", TransformationType.DATE_FORMAT_CONVERSION, 
                             isRequired = false)

        // When
        val result = domainService.applyRule(sourceData, rule)

        // Then - should return the original value when transformation fails
        assertEquals("value1", result)
    }

    companion object {
        @JvmStatic
        fun nestedFieldTestCases(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    mapOf("simple" to "value"),
                    "simple",
                    "value"
                ),
                Arguments.of(
                    mapOf("level1" to mapOf("level2" to "nested")),
                    "level1.level2",
                    "nested"
                ),
                Arguments.of(
                    mapOf("a" to mapOf("b" to mapOf("c" to 123))),
                    "a.b.c",
                    123
                ),
                Arguments.of(
                    mapOf("user" to mapOf("profile" to null)),
                    "user.profile.name",
                    null
                )
            )
        }
    }

    @ParameterizedTest
    @MethodSource("nestedFieldTestCases")
    fun `getNestedValue should handle various nested structures`(
        data: Map<String, Any?>,
        fieldPath: String,
        expected: Any?
    ) {
        val result = domainService.getNestedValue(data  as Map<String, Any>?, fieldPath)
        assertEquals(expected, result)
    }

    private fun createRule(
        id: String,
        sourceField: String,
        targetField: String,
        transformationType: TransformationType,
        isRequired: Boolean = false,
        defaultValue: String? = null,
        order: Int = 0
    ): FieldMappingRule {
        return FieldMappingRule(
            id = id,
            sourceField = sourceField,
            sourceLabel = sourceField,
            targetField = targetField,
            targetLabel = targetField,
            transformationType = transformationType,
            transformationConfig = null,
            isRequired = isRequired,
            defaultValue = defaultValue,
            profileId = "test-profile",
            order = order
        )
    }

    private fun createProfile(rules: List<FieldMappingRule>): FieldMappingProfile {
        return FieldMappingProfile(
            id = "test-profile",
            name = "Test Profile",
            description = "Test profile for domain service tests",
            companyId = 12345L,
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            rules = rules
        )
    }
}
